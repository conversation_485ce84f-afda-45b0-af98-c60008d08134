�AnalyticsFina�ap�unspecifie�detachedConfiguration��com.android.tools.buil�aapt�8.9.1-1278265�runtim���� ��AnalyticsFina�ap�unspecifie�debugCompileClasspat��androidx.appcompa�appcompa�1.7.�releaseVariantReleaseApiPublicatio���com.google.android.materia�materia�1.12.�	��androidx.activit�activit�1.10.�	��androidx.constraintlayou�constraintlayou�2.2.�	��com.github.nSella1�UserAnalyticsSD�v1.0.�	��com.squareup.retrofit�retrofi�2.11.�apiElement���converter-gso���appcompat-resource�	�androidx.annotatio�annotatio�1.8.�jvmApiElements-publishe��androidx.cor�cor�1.13.�	�androidx.cursoradapte�cursoradapte�1.0.�compil��androidx.drawerlayou�drawerlayou�1.1.�&�androidx.fragmen�fragmen�1.5.�	�androidx.savedstat�savedstat�1.2.�	�androidx.cardvie�cardvie�%&�androidx.coordinatorlayou�coordinatorlayou�1.1.�&�androidx.dynamicanimatio�dynamicanimatio�%&�annotation-experimenta�1.4.�	�androidx.lifecycl�lifecycle-runtim�2.6.�	�androidx.recyclervie�recyclervie�4&�androidx.resourceinspectio�resourceinspection-annotatio�1.0.��androidx.transitio�transitio�1.5.�	�androidx.vectordrawabl�vectordrawabl�4&�androidx.viewpager�viewpager�%&�org.jetbrains.kotli�kotlin-stdli�1.8.2�&� core-kt�"	� core-viewtre�%	�9lifecycle-commo�;�9lifecycle-viewmode�;	�9lifecycle-viewmodel-savedstat�;	�com.squareup.okhttp�okhtt�4.9.��com.google.code.gso�gso�2.10.�&�Dvectordrawable-animate�4&�annotation-jv��androidx.versionedparcelabl�versionedparcelabl�)&�androidx.customvie�customvie�4&�androidx.collectio�collectio�4&�9lifecycle-livedata-cor�;	�androidx.loade�loade�%&�androidx.viewpage�viewpage�%&�androidx.legac�legacy-support-core-util�%&�androidx.arch.cor�core-commo�2.2.��Hkotlin-stdlib-commo�J&�org.jetbrain�annotation�23.0.�&�org.jetbrains.kotlin�kotlinx-coroutines-androi�1.7.��com.squareup.oki�oki�2.8.�jvm-ap��androidx.interpolato�interpolato�%&�9lifecycle-livedat�;	�androidx.documentfil�documentfil�%&�androidx.localbroadcastmanage�localbroadcastmanage�%&�androidx.prin�prin�%&�lkotlinx-coroutines-cor�n�lkotlinx-coroutines-bo�nplatform-compil��Hkotlin-stdlib-jdk�J&�ecore-runtim�g	�lkotlinx-coroutines-core-jv�n�Hkotlin-stdlib-jdk�J&�� ������������������������������	��
��
��������
�������������������������������������������������������������� ��!��"��#��$��$��$��%��&��'��(��$��)��*��$��+��,��-��.��/��0��0��0��0��1��2��3��3��3��3��3��3��3��3��4��5��5��6��7��8��9��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��:��;��<��<��<��=��>��?��@��A��B��C��D��D��E��E��F��G��G��G��G��G��G��G��G��G��G��G��G��G��G��G��G��G��G��H��H��H��I��I��J��K�