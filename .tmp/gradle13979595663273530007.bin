�com.android.tools.buil�aapt�8.9.1-1278265��   � roo�  :ap�AnalyticsFina�ap�unspecifie�� detachedConfiguration�    	
 �requeste�Googl�� �� �� � ����androidx.appcompa�appcompa�1.7.��   �com.google.android.materia�materia�1.12.�   �androidx.activit�activit�1.10.�   �androidx.constraintlayou�constraintlayou�2.2.�   �com.github.nSella1�UserAnalyticsSD�v1.0.�   �com.squareup.retrofit�retrofi�2.11.�   �converter-gso�   �   �	
   �   �
   �   �   �   �	
   �androidx.annotatio�annotatio�1.3.�   �appcompat-resource�   �androidx.cor�cor�1.13.�   �androidx.cursoradapte�cursoradapte�1.0.�   �androidx.drawerlayou�drawerlayou�   �androidx.fragmen�fragmen�1.5.�   �androidx.savedstat�savedstat�1.2.�   �1.8.�(   �   �   �   � !1.1.�)   �"#$$   �%&''   �	
1.8.�   �1.2.�   �1.6.�   �androidx.cardvie�cardvie�   �androidx.coordinatorlayou�coordinatorlayou�1.1.�   �
2.0.�   �1.6.�   � !)   �androidx.dynamicanimatio�dynamicanimatio�   �annotation-experimenta�   �"#1.2.�   �androidx.lifecycl�lifecycle-runtim�2.0.�   �androidx.recyclervie�recyclervie�   �androidx.resourceinspectio�resourceinspection-annotatio�1.0.�   �androidx.transitio�transitio�1.5.�   �androidx.vectordrawabl�vectordrawabl�1   �androidx.viewpager�viewpager�   �-.   �/011   �45   �61.4.�G   �892.6.�H   �;<11   �=>??   �@ABB   �CD11   �EF   �(   �core-kt�   �core-viewtre�   �8lifecycle-commo�2.6.�   �89L   �8lifecycle-viewmode�L   �8lifecycle-viewmodel-savedstat�L   �org.jetbrains.kotli�kotlin-stdli�   �OP1.8.2�   �I   �J   �8KHH   �8MHH   �8NHH   �OPQQ   �com.squareup.okhttp�okhtt�3.14.�   �RS4.9.�U   �   �com.google.code.gso�gso�2.10.�   �VWXX   �Cvectordrawable-animate�1   �   �CY11   �annotation-jv�(   �Z((   �3   �6G   �89H   �androidx.versionedparcelabl�versionedparcelabl�)   �[\))   �   �1   �+   �androidx.customvie�customvie�1   �]^11   �	
1.5.�   �androidx.collectio�collectio�1   �I+   �8lifecycle-livedata-cor�2.5.�   �8Mc   �8Nc   �androidx.loade�loade�   �%&+   �androidx.viewpage�viewpage�   �OP1.6.2�   �`a11   �8bHH   �de   �fg   �OP1.8.1�   �1   �]^   �`a   �   �androidx.legac�legacy-support-core-util�   �jk   �OP1.7.1�   �androidx.arch.cor�core-commo�2.2.�   �8KH   �8bH   �8MH   �8NH   �mnoo   �"#1   �;<1   �Okotlin-stdlib-commo�Q   �org.jetbrain�annotation�13.�   �OpQQ   �qr23.0.�t   �org.jetbrains.kotlin�kotlinx-coroutines-androi�1.6.�   �uv1.7.�x   �com.squareup.oki�oki�2.8.�   �OP1.4.1�   �yz{{   �androidx.interpolato�interpolato�   �}~   �   �8lifecycle-livedat�:   �8M:   �8H   �8HH   �androidx.documentfil�documentfil�   �androidx.localbroadcastmanage�localbroadcastmanage�   �androidx.prin�prin�   ���   ���   ���   �ukotlinx-coroutines-cor�x   �ukotlinx-coroutines-bo�x    org.gradle.categor�platfor� �Okotlin-stdlib-jdk�1.8.2�   �u�xx   �u�xx   �O�QQ   �OPG   �OpG   �mcore-runtim�2.1.�   �m�oo   �ukotlinx-coroutines-core-jv�x   �u�xx   �uvx   �Okotlin-stdlib-jdk�Q   �O�QQ   �qrt   �Op�    � roo� � ��  ��version resolved in configuration ':app:debugRuntimeClasspath' by consistent resolutionrequeste�by ancesto�appcompat-resources is in atomic group androidx.appcompa�Googl� ��  ����� ��  ������ ��  ������ ��  ���mave� ��  ����MavenRep� ��  ����� ��  ���appcompat is in atomic group androidx.appcompa��� ��  ����� ���� ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ��  ����� ���� ����� ��  ����� ��  ����� ��  ����� ��  ����� �� �@� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� ���� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ���� ���	� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���
� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ���� � ���� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���� ���� ���� ���� ���� ���� � ��� � ���� ���� ���� ���� ���� ���� � ��� � ���� ���� ���� ���� ���� ���� � ��� � ��� � ���� ���� ���� ���� ���� ���	� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ���� � ��� � ��� � ��� � ��� � ���� � ���� � ��� � ���� � ���� � ��� � ���� � ���� � ��� � ���� � ���� � ���� � ���� � ���� � ���� � ���� � ���� � ���� � ���� ���� � ��� � ���� � ��� � ���� � ���� � ��� � ��� � ��� � ����� ���� ���� ���� � ��� � ��� � ���� � ��� � ���� � ���� � ��� � ���