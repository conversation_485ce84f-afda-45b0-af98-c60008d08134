�androidx.appcompa�appcompa�1.7.��   �com.google.android.materia�materia�1.12.�   �androidx.activit�activit�1.10.�   �androidx.constraintlayou�constraintlayou�2.2.�   �com.github.nSella1�UserAnalyticsSD�v1.0.�   �com.squareup.retrofit�retrofi�2.11.�   �converter-gso�   �	
   �androidx.annotatio�annotatio�1.3.�   �appcompat-resource�   �androidx.collectio�collectio�1.0.�   �androidx.cor�cor�1.13.�   �core-kt�   �androidx.cursoradapte�cursoradapte�   �androidx.drawerlayou�drawerlayou�   �androidx.emoji�emoji�   �%emoji2-views-helpe�1.2.�   �androidx.fragmen�fragmen�1.5.�   �androidx.lifecycl�lifecycle-runtim�2.6.�   �,lifecycle-viewmode�.   �androidx.profileinstalle�profileinstalle�1.3.�   �androidx.resourceinspectio�resourceinspection-annotatio�1.0.�   �androidx.savedstat�savedstat�1.2.�   �org.jetbrains.kotli�kotlin-stdli�1.8.2�   �9kotlin-bo�;    org.gradle.categor�platfor� �com.google.errorpron�error_prone_annotation�2.15.�   �	
1.8.�   �(   �1.6.�   �androidx.cardvie�cardvie�   �androidx.coordinatorlayou�coordinatorlayou�1.1.�   �
2.0.�   �1.6.�   �#$1.1.�   �androidx.dynamicanimatio�dynamicanimatio�   �annotation-experimenta�   �)*1.2.�   �,-2.0.�   �androidx.recyclervie�recyclervie�   �androidx.transitio�transitio�1.5.�   �androidx.vectordrawabl�vectordrawabl�H   �androidx.viewpager�viewpager�   �1.8.�   �core-viewtre�   �,lifecycle-commo�.   �,lifecycle-viewmodel-savedstat�.   �011.4.�   �androidx.tracin�tracin�   �9:   �org.jetbrains.kotlin�kotlinx-coroutines-cor�1.7.�   �(   �constraintlayout-cor�K   �1.3.�   �com.squareup.okhttp�logging-intercepto�4.9.�   �fokhtt�3.14.�   �com.google.code.gso�gso�2.10.�   �Vvectordrawable-animate�H   �   �J   �N^   �androidx.concurren�concurrent-future�   �androidx.interpolato�interpolato�   �,-2.6.�   �androidx.versionedparcelabl�versionedparcelabl�K   �H   �H   �   �,lifecycle-proces�2.4.�   �androidx.startu�startup-runtim�   �%'   �	
1.5.�   � (   �,lifecycle-livedata-cor�2.5.�   �,/|   �,]|   �androidx.loade�loade�   �67(   �androidx.viewpage�viewpage�   �9:1.6.2�   �9:1.8.1�   �,{.   �,v.   �androidx.arch.cor�core-commo�2.1.�   �9kotlin-stdlib-commo�;   �org.jetbrain�annotation�13.�   �(   �androidx.customvie�customvie�H   �H   ���   �   �androidx.legac�legacy-support-core-util�   ��legacy-support-core-u�   �)*H   �QRH   �annotation-jv�Z   �opH   �xyK   �com.google.guav�listenablefutur�1.�   �akotlinx-coroutines-androi�1.6.�   �akotlinx-coroutines-core-jv�c   �fih   �9kotlin-stdlib-jdk�1.4.1�   �9�;   �9:1.7.1�   ���2.2.�   ��core-runtim�   �,\s   �01   �,{s   �,vs   �,/s   �,]s   �,lifecycle-livedat�P   �,/P   �,�s   �androidx.documentfil�documentfil�   �androidx.localbroadcastmanage�localbroadcastmanage�   �androidx.prin�prin�   ���23.0.�   �akotlinx-coroutines-bo�c     �9�1.8.2�   �9��   �com.squareup.oki�oki�2.8.�   �9:�   �9kotlin-stdlib-jdk�;   ����   �a�c   �9:^   �9�^    � roo� � ��  �requeste�appcompat-resources is in atomic group androidx.appcompa�between versions 1.7.0, 1.6.1 and 1.2.�Googl� ��  ���� ��  ��between versions 1.10.1, 1.7.0, 1.8.0 and 1.5.��� ��  ��between versions 2.2.1 and 2.0.��� ��  ��mave� ��  ��MavenRep� ��  ���� ��  ��appcompat is in atomic group androidx.appcompa��� ��  ��constrain��between versions 1.13.0, 1.6.0, 1.3.2, 1.3.0, 1.2.0, 1.1.0 and 1.0.0�� ��  ���between versions 1.13.0 and 1.2.��� ��  ���� ��  ����� ��  ��between versions 1.5.4, 1.2.5 and 1.1.��� ��  ���� ��  ��between versions 1.2.1 and 1.2.��� ��  ����between versions 1.8.22, 1.6.21, 1.8.10, 1.7.10, 1.4.10 and 1.4.0�� ��  ���� ��  ��between versions 1.1.1 and 1.0.��� ��  ���� ��  ���� ��  ���� ��  ���� ��  ���� ��  ���� ��  ���� ��  ��between versions 1.8.1, 1.3.0, 1.2.0, 1.0.0, 1.6.0 and 1.1.��� ���� ��between versions 1.4.0, 1.3.1 and 1.3.��� ��  ���� ��  ���� ��  ����� ���� ���� ��  ���� ��  ���� ��  ���� ��  ��between versions 1.4.0 and 1.0.��� ��  ���between versions 2.6.2, 2.6.1 and 2.0.��� ��  ���� ��  ���� ��  ��between versions 1.1.0 and 1.0.��� ��  ���between versions 1.3.0 and 1.2.��� ��  ���� ��  ���� ��  ���between versions 1.8.22, 1.8.20 and 1.4.��� ��  ����� ��  ���� ��  ����� ��  ���� ��  ����� ��  ����� ��  ���� ��  ����� ��  ��between versions 4.9.3 and 3.14.��� ��  ���between versions 1.8.22, 1.4.10 and 1.8.2��� ��  ��between versions 2.2.0 and 2.1.��� ��  ���between versions 2.6.2 and 2.6.��� ��  ���between versions 2.6.2, 2.6.1 and 2.5.��� ��  ���between versions 2.6.2, 2.6.1 and 2.4.��� ��  ���between versions 2.6.2, 2.6.1, 2.5.1 and 2.0.��� ��  ������ ��  ����� ��  ���between versions 2.6.2 and 2.0.��� ��  ���� ��  ���� ��  ���� ��  ���� ��  ��between versions 23.0.0 and 13.��� ��  ���� ��  ����� ��  ���between versions 1.7.3 and 1.6.��� �� �� � ��� � ��� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���
� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ���� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���� ���� � ��� � ��� � ��� � ��� � ���� ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ��� � ��� � ��� � ���� � ��� � ��� � ��� � ���
� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���� � ��� � ���� ���� ���� ���� ���� ���� ���
� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���
� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���	� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���� � ��� � ��� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���
� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� ���� ���� ���� ���� ���� � ��� � ��� � ���� ���� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ���� � ��� � ��� � ��� � ���� � ��� � ��� � ���	� � ��� � ��� � ��� � ��� � ��� � ��� � ��� � ���� ���� � ���� � ���� � ���� � ��� � ��� � ��� � ���� � ��� � ���� � ���� � ���� � ���� � ��� � ���� � ���� � ��� � ���� � ��� � ���� � ���� � ���� � ���� � ���� � ����� ���� ���� ���� ���� � ���� ���� � ���� � ���� � ��� � ���� � ��� � ���� � ��� � ��� � ����� ���� ���� ���� � ���� � ��� � ��� � ��� � ���� � ��� � ���� � ��� � ���� � ���� � ��� � ���