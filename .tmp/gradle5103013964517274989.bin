�AnalyticsFina�ap�unspecifie�debugRuntimeClasspat��androidx.appcompa�appcompa�1.7.�releaseVariantReleaseRuntimePublicatio���com.google.android.materia�materia�1.12.�	��androidx.activit�activit�1.10.�	��androidx.constraintlayou�constraintlayou�2.2.�	��com.github.nSella1�UserAnalyticsSD�v1.0.�	��com.squareup.retrofit�retrofi�2.11.�runtimeElement���converter-gso���appcompat-resource�	�androidx.cor�cor�1.13.�	�core-kt�	�androidx.cursoradapte�cursoradapte�1.0.�runtim��androidx.emoji�emoji�1.3.�	�androidx.fragmen�fragmen�1.5.�	�androidx.resourceinspectio�resourceinspection-annotatio�1.0.��androidx.savedstat�savedstat�1.2.�	�org.jetbrains.kotli�kotlin-stdli�1.8.2�#�0kotlin-bo�2platform-runtim��androidx.drawerlayou�drawerlayou�1.1.�#�com.google.errorpron�error_prone_annotation�2.15.�#�androidx.cardvie�cardvie�"#�androidx.coordinatorlayou�coordinatorlayou�1.1.�#�androidx.dynamicanimatio�dynamicanimatio�"#�androidx.transitio�transitio�1.5.�	�androidx.vectordrawabl�vectordrawabl�?#�androidx.viewpager�viewpager�"#�androidx.annotatio�annotatio�1.8.�jvmRuntimeElements-publishe��androidx.profileinstalle�profileinstalle�1.4.�	�core-viewtre�"	�androidx.tracin�tracin�"releaseRuntimePublicatio��org.jetbrains.kotlin�kotlinx-coroutines-cor�1.7.�L�constraintlayout-cor�7�com.squareup.okhttp�logging-intercepto�4.9.��com.google.code.gso�gso�2.10.�#�Evectordrawable-animate�?#�Iannotation-experimenta�O	�androidx.lifecycl�lifecycle-runtim�2.6.�	�androidx.interpolato�interpolato�"#�androidx.versionedparcelabl�versionedparcelabl�7#�androidx.collectio�collectio�?#�$emoji2-views-helpe�&	�androidx.loade�loade�"#�androidx.viewpage�viewpage�"#�0kotlin-stdlib-commo�2#�androidx.customvie�customvie�?#�androidx.legac�legacy-support-core-util�"#�androidx.recyclervie�recyclervie�?#�Iannotation-jv�KL�androidx.concurren�concurrent-future�?�androidx.startu�startup-runtim�7	�com.google.guav�listenablefutur�1.�#�Tkotlinx-coroutines-core-jv�VL�Xokhtt�Z�0kotlin-stdlib-jdk�2#�androidx.arch.cor�core-commo�2.2.��`lifecycle-commo�b�`lifecycle-livedata-cor�b	�`lifecycle-proces�b	�`lifecycle-viewmode�b	�`lifecycle-viewmodel-savedstat�b	��core-runtim�	�`lifecycle-livedat�b	�androidx.documentfil�documentfil�"#�androidx.localbroadcastmanage�localbroadcastmanage�"#�androidx.prin�prin�"#�Tkotlinx-coroutines-bo�V4�org.jetbrain�annotation�23.0.�#�com.squareup.oki�oki�2.8.�jvm-runtim��0kotlin-stdlib-jdk�2#�Tkotlinx-coroutines-androi�V�� ������������������������������������	��	��
��
��
������������
��
���������������������������������������������������������������������������� ��!��"��#��$��%��&��'��(��(��(��(��(��(��)��*��+��(��,��-��(��.��/��0��1��2��3��4��4��5��6��6��6��6��7��7��7��8��8��9��:��;��<��=��=��=��=��=��=��=��=��=��=��=��=��=��=��=��>��>��?��?��?��@��@��@��@��@��A��B��C��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��D��E��F��G��G��G��H��I��J��J��K��K��L��M��N��N��O��P��P��P��Q��R��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��S��T��U��V��V��V��W��W��X��X�