<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="@color/primary_blue_darker"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/menu_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        app:tint="@color/text_white" />

    <TextView
        android:id="@+id/menu_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/text_white"
        android:textSize="16sp"
        android:fontFamily="sans-serif" />

</LinearLayout>
