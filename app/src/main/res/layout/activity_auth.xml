<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <RelativeLayout
        android:id="@+id/authRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <!-- תיבה לבנה מרכזית -->
        <LinearLayout
            android:id="@+id/formCard"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:elevation="8dp"
            android:padding="20dp"
            android:layout_centerInParent="true"
            android:layout_margin="12dp"
            android:gravity="center">

            <!-- כותרת -->
            <TextView
                android:id="@+id/titleText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ברוכים הבאים\nל-MyInterest"
                android:textSize="18sp"
                android:textColor="@color/primary_blue_dark"
                android:textStyle="bold"
                android:gravity="center"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="8dp"
                android:lineSpacingExtra="2dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="התחברו או הירשמו כדי להתחיל"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- טאבים -->
            <LinearLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:background="#F5F5F5"
                android:padding="4dp"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/loginTab"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:text="התחברות"
                    android:textSize="14sp"
                    android:textColor="#FFFFFF"
                    android:textStyle="bold"
                    android:background="@drawable/tab_selected"
                    android:layout_margin="2dp" />

                <Button
                    android:id="@+id/signupTab"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:text="הרשמה"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:textStyle="bold"
                    android:background="@drawable/tab_unselected"
                    android:layout_margin="2dp" />
            </LinearLayout>

            <!-- טופס מתחלף -->
            <ViewFlipper
                android:id="@+id/formSwitcher"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inAnimation="@android:anim/slide_in_left"
                android:outAnimation="@android:anim/slide_out_right"
                android:layout_marginTop="12dp">

                <!-- טופס LOGIN -->
                <LinearLayout
                    android:id="@+id/loginForm"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText android:id="@+id/loginEmail"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="כתובת אימייל"
                        android:inputType="textEmailAddress"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/loginPassword"/>

                    <EditText android:id="@+id/loginPassword"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="סיסמה"
                        android:inputType="textPassword"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionDone"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="שכחת סיסמה?"
                        android:textColor="@color/primary_blue"
                        android:textSize="14sp"
                        android:layout_marginTop="8dp" />

                    <Button
                        android:id="@+id/loginButton"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:text="התחברות"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/button_primary"
                        android:textColor="@color/text_white"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif-medium"
                        android:elevation="4dp"/>
                </LinearLayout>

                <!-- טופס SIGNUP -->
                <LinearLayout
                    android:id="@+id/signupForm"
                    android:orientation="vertical"
                    android:paddingTop="8dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText android:id="@+id/signupFirstName"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="שם פרטי"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/signupLastName"/>

                    <EditText android:id="@+id/signupLastName"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="שם משפחה"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/signupEmail"/>

                    <EditText android:id="@+id/signupEmail"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="כתובת אימייל"
                        android:inputType="textEmailAddress"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/signupPassword"/>

                    <EditText android:id="@+id/signupPassword"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="סיסמה"
                        android:inputType="textPassword"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/signupAge"/>

                    <EditText android:id="@+id/signupAge"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:hint="גיל"
                        android:inputType="number"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:textSize="16sp"
                        android:gravity="right|center_vertical"
                        android:textDirection="rtl"
                        android:imeOptions="actionNext"
                        android:nextFocusDown="@+id/signupGender"/>

                    <Spinner
                        android:id="@+id/signupGender"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/edit_text_background"
                        android:padding="16dp"/>

                    <Button
                        android:id="@+id/signupButton"
                        android:layout_width="match_parent"
                        android:layout_height="52dp"
                        android:text="הרשמה"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/button_primary"
                        android:textColor="@color/text_white"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif-medium"
                        android:elevation="4dp"/>
                </LinearLayout>
            </ViewFlipper>
        </LinearLayout>
    </RelativeLayout>
</ScrollView>
