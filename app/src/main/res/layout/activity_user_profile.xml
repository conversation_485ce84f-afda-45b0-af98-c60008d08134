<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#f5f5f5">

    <TextView
        android:id="@+id/profile_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="פרופיל"
        android:textAlignment="center"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:paddingTop="32dp"
        android:paddingBottom="16dp" />

    <TextView
        android:id="@+id/profile_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="שם"
        android:textSize="20sp"
        android:padding="8dp" />

    <Button
        android:id="@+id/btn_edit_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ערוך שם"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/profile_email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="מייל"
        android:textSize="20sp"
        android:padding="8dp" />

    <TextView
        android:id="@+id/profile_age"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="גיל"
        android:textSize="20sp"
        android:padding="8dp" />

    <TextView
        android:id="@+id/profile_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="מין"
        android:textSize="20sp"
        android:padding="8dp" />
</LinearLayout>
