//app/build.gradle
plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.analytics.analyticsfinal'
    compileSdk 35

    defaultConfig {
        applicationId "com.analytics.analyticsfinal"
        minSdk 26
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core


    // 📦 ספריית האנליטיקה המקומית
    //implementation project(':analyticstracker')


    // jitpack
    implementation libs.useranalyticsSdk


    // Retrofit
    implementation libs.retrofit

// ממיר JSON באמצעות Gson
    implementation libs.converter.gson
}