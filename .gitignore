# === SECURITY WARNING ===
# קבצים עם מידע רגיש - אסור שיהיו ב-Git!
# יש ליצור את הקבצים האלה מקומית לפי ההוראות ב-README
**/config/TrackerConfig.java
backend/src/main/resources/application.properties
local.properties



# === Node / React (frontend) ===
frontend/node_modules/
frontend/build/
frontend/.env
frontend/package-lock.json

# === Android ===
*.iml
.gradle/
.local.properties
.idea/
android/.gradle/
android/build/
android/.cxx/
android/.externalNativeBuild/
android/captures/
android/output.json
android/local.properties

# === Java / Gradle ===
build/
bin/
.gradle/
**/build/
caches/
daemon/
native/


# Allow JitPack-required Gradle wrapper files
!gradle/wrapper/gradle-wrapper.jar
!gradle/wrapper/gradle-wrapper.properties
!gradlew
!gradlew.bat


# === VS Code ===
.vscode/
*.log

# === OS Files ===
.DS_Store
Thumbs.db

# === Git ===
!.gitignore
a n a l y t i c s t r a c k e r / s r c / m a i n / j a v a / c o m / a n a l y t i c s / a n a l y t i c s t r a c k e r / c o n f i g / T r a c k e r C o n f i g . j a v a 
 
 