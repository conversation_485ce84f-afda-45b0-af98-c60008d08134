package com.analytics.analyticstracker;


/**
 * דוגמה לקובץ קונפיגורציה עבור AnalyticsTracker
 *
 * 👉 לפני השימוש: העתיקו את הקובץ ל־TrackerConfig.java
 *    ושנו את הערכים בהתאם לפרויקט שלכם.
 */
public class TrackerConfig {
    /** כתובת השרת הבסיסית */
    public static final String BASE_URL = "";

    /** מפתח API */
    public static final String DEFAULT_API_KEY = "";

    /** Timeout בדקות */
    public static final int CONNECTION_TIMEOUT = 30;
    public static final int READ_TIMEOUT = 30;

    /** מצב DEBUG */
    public static final boolean DEBUG_MODE = false;
}
