# =============================================================================
# User Analytics SDK - Local Development Configuration
# =============================================================================
# This file is used for local development on your computer
# Contains actual values for testing - NEVER COMMIT SECRETS TO GIT!
# 
# To use this profile: --spring.profiles.active=local
# Or set environment variable: SPRING_PROFILES_ACTIVE=local
# =============================================================================

# Application Information
spring.application.name=User Analytics SDK (Local)

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB Atlas Connection (actual connection string for development)
spring.data.mongodb.uri=mongodb+srv://noamsell:<EMAIL>/user-analytics?retryWrites=true&w=majority&appName=AnalyticsCluster
spring.data.mongodb.auto-index-creation=true

# =============================================================================
# SERVER CONFIGURATION  
# =============================================================================
server.address=0.0.0.0
server.port=8080

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret Key for development (use a different one in production!)
jwt.secret=myLocalDevSecretKey123456789012345678901234567890

# =============================================================================
# ACTUATOR & HEALTH CHECKS
# =============================================================================
# Health endpoints for development
management.endpoints.web.exposure.include=health,info,metrics,env
management.endpoint.health.show-details=always
management.health.defaults.enabled=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Development logging levels (more verbose for debugging)
logging.level.root=INFO
logging.level.com.analytics.user_analytics=DEBUG
logging.level.org.springframework.data.mongodb=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG

# =============================================================================
# DEVELOPMENT FEATURES
# =============================================================================
# Enable development tools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# =============================================================================
# DEVELOPMENT COMPATIBILITY
# =============================================================================
# Disable problematic auto-configurations for consistency with production
spring.autoconfigure.exclude=org.springframework.boot.actuate.autoconfigure.metrics.SystemMetricsAutoConfiguration