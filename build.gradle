plugins {
    id 'com.android.library'
    id 'maven-publish'
}

group = 'com.github.nSella10'
version = '1.0.0'

android {
    namespace 'com.analytics.analyticstracker'
    compileSdk 35

    defaultConfig {
        minSdk 26

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
//    implementation libs.appcompat
//    implementation libs.material
//    testImplementation libs.junit
//    androidTestImplementation libs.ext.junit
//    androidTestImplementation libs.espresso.core
//
//    // Retrofit
//    implementation libs.retrofit
//    // ממיר JSON באמצעות Gson
//    implementation libs.converter.gson
//    implementation libs.logging.interceptor

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'

}

publishing {
    publications {
        release(MavenPublication) {
            from components.release
            groupId = 'com.github.nSella10'
            artifactId = 'AnalyticsTracker'
            version = '1.0.0'
        }
    }
}
