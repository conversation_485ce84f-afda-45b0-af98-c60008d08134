[versions]
agp = "8.9.1"
converterGson = "2.11.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
loggingInterceptor = "4.9.3"
material = "1.12.0"
activity = "1.10.1"
constraintlayout = "2.2.1"
identityJvm = "202411.1"
userAnalyticsSdk = "08ab786e41"

[libraries]
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "loggingInterceptor" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
identity-jvm = { group = "com.android.identity", name = "identity-jvm", version.ref = "identityJvm" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "converterGson" }
useranalyticsSdk = { module = "com.github.nSella10:UserAnalyticsSDK", version.ref = "userAnalyticsSdk" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }

